using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ComplaintManagementSystem.Data;
using ComplaintManagementSystem.Models;

namespace ComplaintManagementSystem.Services
{
    public class ComplaintService
    {
        private readonly ComplaintContext _context;

        public ComplaintService(ComplaintContext context)
        {
            _context = context;
        }

        public async Task<List<Complaint>> GetAllComplaintsAsync()
        {
            return await _context.Complaints
                .Include(c => c.ComplaintType)
                .Include(c => c.User)
                .ToListAsync();
        }

        public async Task<Complaint> GetComplaintByIdAsync(int id)
        {
            return await _context.Complaints
                .Include(c => c.ComplaintType)
                .Include(c => c.User)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<List<Complaint>> GetComplaintsByStatusAsync(ComplaintStatus status)
        {
            return await _context.Complaints
                .Include(c => c.ComplaintType)
                .Include(c => c.User)
                .Where(c => c.Status == status)
                .ToListAsync();
        }

        public async Task<List<Complaint>> GetComplaintsByTypeAsync(int typeId)
        {
            return await _context.Complaints
                .Include(c => c.ComplaintType)
                .Include(c => c.User)
                .Where(c => c.ComplaintTypeId == typeId)
                .ToListAsync();
        }

        public async Task<bool> CreateComplaintAsync(Complaint complaint)
        {
            try
            {
                _context.Complaints.Add(complaint);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateComplaintAsync(Complaint complaint)
        {
            try
            {
                _context.Complaints.Update(complaint);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteComplaintAsync(int id)
        {
            try
            {
                var complaint = await _context.Complaints.FindAsync(id);
                if (complaint != null)
                {
                    _context.Complaints.Remove(complaint);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<ComplaintType>> GetComplaintTypesAsync()
        {
            return await _context.ComplaintTypes.ToListAsync();
        }

        public async Task<bool> CreateComplaintTypeAsync(ComplaintType complaintType)
        {
            try
            {
                _context.ComplaintTypes.Add(complaintType);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<Dictionary<string, int>> GetComplaintStatisticsAsync()
        {
            var stats = new Dictionary<string, int>();

            stats["Total"] = await _context.Complaints.CountAsync();
            stats["Pending"] = await _context.Complaints.CountAsync(c => c.Status == ComplaintStatus.Pending);
            stats["InProgress"] = await _context.Complaints.CountAsync(c => c.Status == ComplaintStatus.InProgress);
            stats["Resolved"] = await _context.Complaints.CountAsync(c => c.Status == ComplaintStatus.Resolved);
            stats["Closed"] = await _context.Complaints.CountAsync(c => c.Status == ComplaintStatus.Closed);

            return stats;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
